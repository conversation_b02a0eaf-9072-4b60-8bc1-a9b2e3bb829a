package com.moego.svc.organization.controller;

import com.moego.idl.models.organization.v1.ServiceAreaSearchView;
import com.moego.idl.service.organization.v1.SearchServiceAreasRequest;
import com.moego.idl.service.organization.v1.SearchServiceAreasResponse;
import com.moego.idl.service.organization.v1.ServiceAreaServiceGrpc;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;

@Controller
@RequiredArgsConstructor
public class ServiceAreaController extends ServiceAreaServiceGrpc.ServiceAreaServiceImplBase {

    private final IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Override
    public void searchServiceAreas(
            final SearchServiceAreasRequest request,
            final StreamObserver<SearchServiceAreasResponse> responseObserver) {
        var companyId = request.getCompanyId();

        var areasByLocation = iBusinessServiceAreaClient.getAreasByLocation(new BatchGetAreasByLocationParams(
                null,
                companyId,
                request.getCandidateAreaIdsList().stream().map(Long::intValue).toList(),
                request.getLocationsList().stream()
                        .map(k -> {
                            var param = new GetAreasByLocationParams(k.getIndex(), "", "", k.getZipcode());
                            param.setLat(k.getLat());
                            param.setLng(k.getLng());
                            return param;
                        })
                        .toList()));

        responseObserver.onNext(SearchServiceAreasResponse.newBuilder()
                .addAllResults(areasByLocation.entrySet().stream()
                        .map(entry -> SearchServiceAreasResponse.SearchResult.newBuilder()
                                .setIndex(entry.getKey())
                                .addAllServiceAreas(entry.getValue().stream()
                                        .map(dto -> ServiceAreaSearchView.newBuilder()
                                                .setId(dto.getAreaId())
                                                .setName(dto.getAreaName())
                                                .build())
                                        .toList())
                                .build())
                        .toList())
                .build());
        responseObserver.onCompleted();
    }
}
